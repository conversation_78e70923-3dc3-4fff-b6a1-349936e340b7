#!/usr/bin/env python3
"""
Test script to verify OpenRouter integration works correctly.

This script tests:
1. Configuration loading for OpenRouter
2. Model factory creation with OpenRouter provider
3. Basic API connectivity (if API key is provided)
"""

import asyncio
import logging
import os
import sys

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "."))

from app.autogen_service.model_factory import ModelFactory
from app.shared.config.base import clear_settings_cache, get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_configuration():
    """Test OpenRouter configuration loading."""
    logger.info("Testing OpenRouter configuration...")

    # Set test environment variables
    os.environ["MODEL_PROVIDER"] = "openrouter"
    os.environ["OPENROUTER_API_KEY"] = "test-key"
    os.environ["OPENROUTER_MODEL"] = "openai/gpt-4o-mini"
    os.environ["OPENROUTER_SITE_URL"] = "https://test.com"
    os.environ["OPENROUTER_SITE_NAME"] = "Test App"

    # Clear cache and reload settings
    clear_settings_cache()
    settings = get_settings()

    # Verify configuration
    assert (
        settings.model_provider == "openrouter"
    ), f"Expected 'openrouter', got '{settings.model_provider}'"
    assert (
        settings.openrouter.api_key == "test-key"
    ), "OpenRouter API key not loaded correctly"
    assert (
        settings.openrouter.model == "openai/gpt-4o-mini"
    ), "OpenRouter model not loaded correctly"
    assert (
        settings.openrouter.site_url == "https://test.com"
    ), "OpenRouter site URL not loaded correctly"
    assert (
        settings.openrouter.site_name == "Test App"
    ), "OpenRouter site name not loaded correctly"

    logger.info("✓ Configuration test passed")


def test_model_factory_creation():
    """Test model factory can create OpenRouter clients."""
    logger.info("Testing model factory with OpenRouter...")

    # Ensure OpenRouter is selected
    os.environ["MODEL_PROVIDER"] = "openrouter"
    clear_settings_cache()

    config = {
        "provider": "OpenAIChatCompletionClient",
        "llm_type": "openai",
        "model": "openai/gpt-4o-mini",
        "temperature": 0.7,
        "max_tokens": 100,
    }

    client = ModelFactory.create_model_client(config)

    assert client is not None, "Failed to create OpenRouter client"
    assert hasattr(client, "model"), "Client missing model attribute"

    # Check that the model format is correct for OpenRouter
    expected_model = "openai/gpt-4o-mini"  # OpenRouter format
    assert (
        client.model == expected_model
    ), f"Expected model '{expected_model}', got '{client.model}'"

    logger.info("✓ Model factory creation test passed")


async def test_openrouter_api_connectivity():
    """Test OpenRouter API connectivity (if real API key is provided)."""
    logger.info("Testing OpenRouter API connectivity...")

    # Check if real API key is provided
    real_api_key = os.getenv("OPENROUTER_API_KEY")
    if (
        not real_api_key
        or real_api_key == "test-key"
        or real_api_key.startswith("your-")
    ):
        logger.info("⚠️  Skipping API connectivity test - no real API key provided")
        logger.info(
            "   To test API connectivity, set OPENROUTER_API_KEY environment variable"
        )
        return

    try:
        # Test fetching models from OpenRouter
        models = await ModelFactory.fetch_openrouter_models()

        if models:
            logger.info(
                f"✓ Successfully fetched {len(models)} models from OpenRouter API"
            )

            # Show a few example models
            logger.info("Sample models:")
            for model in models[:3]:
                logger.info(
                    f"  - {model.get('id', 'Unknown')}: {model.get('name', 'No name')}"
                )
        else:
            logger.warning("⚠️  No models returned from OpenRouter API")

    except Exception as e:
        logger.error(f"✗ API connectivity test failed: {str(e)}")
        raise


def test_provider_switching():
    """Test switching between providers."""
    logger.info("Testing provider switching...")

    # Test Requesty
    os.environ["MODEL_PROVIDER"] = "requesty"
    clear_settings_cache()
    settings = get_settings()
    assert settings.model_provider == "requesty", "Failed to switch to Requesty"

    # Test OpenRouter
    os.environ["MODEL_PROVIDER"] = "openrouter"
    clear_settings_cache()
    settings = get_settings()
    assert settings.model_provider == "openrouter", "Failed to switch to OpenRouter"

    # Test OpenAI
    os.environ["MODEL_PROVIDER"] = "openai"
    clear_settings_cache()
    settings = get_settings()
    assert settings.model_provider == "openai", "Failed to switch to OpenAI"

    logger.info("✓ Provider switching test passed")


def test_model_support_checking():
    """Test model support checking for different providers."""
    logger.info("Testing model support checking...")

    # Set to OpenRouter
    os.environ["MODEL_PROVIDER"] = "openrouter"
    clear_settings_cache()

    # Note: This will return False if no API connection, but shouldn't crash
    try:
        is_supported = ModelFactory.is_model_supported("openai/gpt-4o-mini")
        logger.info(f"Model support check returned: {is_supported}")
        logger.info("✓ Model support checking test passed")
    except Exception as e:
        logger.error(f"✗ Model support checking failed: {str(e)}")
        raise


async def main():
    """Run all tests."""
    logger.info("Starting OpenRouter Integration Tests")
    logger.info("=" * 50)

    try:
        # Configuration tests
        test_configuration()
        test_provider_switching()

        # Model factory tests
        test_model_factory_creation()
        test_model_support_checking()

        # API tests (if key available)
        await test_openrouter_api_connectivity()

        logger.info("=" * 50)
        logger.info("✓ All tests passed!")
        logger.info("\nOpenRouter integration is working correctly.")
        logger.info("\nTo use OpenRouter in your application:")
        logger.info("1. Set MODEL_PROVIDER=openrouter in your .env file")
        logger.info("2. Add your OPENROUTER_API_KEY")
        logger.info("3. Optionally set OPENROUTER_SITE_URL and OPENROUTER_SITE_NAME")

    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
