#!/usr/bin/env python3
"""
Example demonstrating how to switch between different model providers.

This example shows how to use environment variables to control which
provider (Requesty, OpenRouter, or OpenAI) is used for model routing.
"""

import asyncio
import logging
import os
import sys

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "../.."))

from autogen_core.models import UserMessage

from app.autogen_service.model_factory import ModelFactory
from app.shared.config.base import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_provider(provider_name: str, model: str):
    """Test a specific provider with a given model."""
    logger.info(f"Testing {provider_name} with model: {model}")

    # Set the provider in environment (this would normally be in .env file)
    os.environ["MODEL_PROVIDER"] = provider_name.lower()

    # Clear settings cache to pick up new environment variable
    from app.shared.config.base import clear_settings_cache

    clear_settings_cache()

    # Get updated settings
    settings = get_settings()
    logger.info(f"Current provider setting: {settings.model_provider}")

    config = {
        "provider": "OpenAIChatCompletionClient",
        "llm_type": "openai",
        "model": model,
        "temperature": 0.7,
        "max_tokens": 100,
    }

    client = ModelFactory.create_model_client(config)
    if client:
        try:
            messages = [
                UserMessage(
                    content=f"Hello! Please respond with 'Hi from {provider_name}!'",
                    source="user",
                )
            ]

            response = await client.create(messages)
            logger.info(f"{provider_name} response: {response.content}")
            return True

        except Exception as e:
            logger.error(f"Error testing {provider_name}: {str(e)}")
            return False
        finally:
            await client.close()
    else:
        logger.error(f"Failed to create client for {provider_name}")
        return False


async def demonstrate_provider_switching():
    """Demonstrate switching between different providers."""
    logger.info("Demonstrating Provider Switching")
    logger.info("=" * 50)

    # Test configurations for different providers
    test_configs = [
        {
            "provider": "requesty",
            "model": "gpt-4o-mini",
            "description": "Requesty routing to OpenAI GPT-4o-mini",
        },
        {
            "provider": "openrouter",
            "model": "openai/gpt-4o-mini",
            "description": "OpenRouter routing to OpenAI GPT-4o-mini",
        },
        {
            "provider": "openrouter",
            "model": "anthropic/claude-3-haiku-********",
            "description": "OpenRouter routing to Anthropic Claude Haiku",
        },
        {
            "provider": "openai",
            "model": "gpt-4o-mini",
            "description": "Direct OpenAI API call",
        },
    ]

    results = []

    for config in test_configs:
        logger.info(f"\n--- Testing: {config['description']} ---")

        # Check if we have the required API keys
        settings = get_settings()

        if config["provider"] == "requesty" and not settings.requesty.api_key:
            logger.warning("Skipping Requesty test - no API key configured")
            continue
        elif config["provider"] == "openrouter" and not settings.openrouter.api_key:
            logger.warning("Skipping OpenRouter test - no API key configured")
            continue
        elif config["provider"] == "openai" and not settings.openai.api_key:
            logger.warning("Skipping OpenAI test - no API key configured")
            continue

        success = await test_provider(config["provider"], config["model"])
        results.append(
            {
                "provider": config["provider"],
                "model": config["model"],
                "success": success,
            }
        )

    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("Test Results Summary:")
    for result in results:
        status = "✓ SUCCESS" if result["success"] else "✗ FAILED"
        logger.info(f"{status} - {result['provider']}: {result['model']}")


async def show_provider_capabilities():
    """Show capabilities of different providers."""
    logger.info("\nProvider Capabilities:")
    logger.info("=" * 30)

    # Requesty capabilities
    logger.info("Requesty:")
    logger.info("  - 300+ models from multiple providers")
    logger.info("  - Unified pricing and billing")
    logger.info("  - Automatic failover")
    logger.info("  - Usage analytics")

    # OpenRouter capabilities
    logger.info("\nOpenRouter:")
    logger.info("  - 200+ models from multiple providers")
    logger.info("  - Competitive pricing")
    logger.info("  - Model routing and fallbacks")
    logger.info("  - Site attribution for rankings")

    # OpenAI capabilities
    logger.info("\nOpenAI Direct:")
    logger.info("  - Latest OpenAI models")
    logger.info("  - Direct API access")
    logger.info("  - Full feature support")
    logger.info("  - Highest reliability")


async def main():
    """Run the provider switching demonstration."""
    logger.info("Provider Switching Example")
    logger.info("=" * 50)

    # Show current configuration
    settings = get_settings()
    logger.info(f"Current MODEL_PROVIDER: {settings.model_provider}")
    logger.info(
        f"Requesty API Key configured: {'Yes' if settings.requesty.api_key else 'No'}"
    )
    logger.info(
        f"OpenRouter API Key configured: {'Yes' if settings.openrouter.api_key else 'No'}"
    )
    logger.info(
        f"OpenAI API Key configured: {'Yes' if settings.openai.api_key else 'No'}"
    )

    # Show provider capabilities
    await show_provider_capabilities()

    # Demonstrate switching
    await demonstrate_provider_switching()

    logger.info("\nProvider Switching Example Complete!")
    logger.info("\nTo switch providers, set the MODEL_PROVIDER environment variable:")
    logger.info("  export MODEL_PROVIDER=requesty")
    logger.info("  export MODEL_PROVIDER=openrouter")
    logger.info("  export MODEL_PROVIDER=openai")


if __name__ == "__main__":
    asyncio.run(main())
    asyncio.run(main())
